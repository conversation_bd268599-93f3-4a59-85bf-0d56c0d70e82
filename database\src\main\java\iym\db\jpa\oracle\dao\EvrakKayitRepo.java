package iym.db.jpa.dao;

import iym.common.model.entity.iym.EvrakKayit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for EvrakKayit entity
 */
@Repository
public interface EvrakKayitRepo extends JpaRepository<EvrakKayit, Long> {

    boolean  existsByEvrakSiraNo(String evrakSiraNo);

    //@Query("SELECT ek from EvrakKayit ek WHERE ek.evrakGeldigiKurumKodu = :iIlIlceKodu and ek.geldigiIlIlceKodu= :gelIlIlceKodu and ek.evrakGeldigiKurumKodu = :evrakGeldigiKurumKodu")
    Optional<EvrakKayit> findByEvrakNoAndGeldigiIlIlceKoduAndEvrakGeldigiKurumKodu(String evrakNo,
                               String gelIlIlceKodu,
                               String evrakGeldigiKurumKodu);


    List<EvrakKayit> findByEvrakTipi(String evrakTipi);
    
    List<EvrakKayit> findByGirisTarihBetween(Date startDate, Date endDate);
    
    List<EvrakKayit> findByDurumu(String durumu);
    
    List<EvrakKayit> findByHavaleBirim(String havaleBirim);
    
    List<EvrakKayit> findByAcilmi(String acilmi);
}
