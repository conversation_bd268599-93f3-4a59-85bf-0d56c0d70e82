package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.MahkemeKararTalep;
import iym.common.service.db.DbMahkemeKararTalepService;
import iym.db.jpa.dao.MahkemeKararTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeKararTalep entity
 */
@Service
public class DbMahkemeKararTalepServiceImpl extends GenericDbServiceImpl<MahkemeKararTalep, Long> implements DbMahkemeKararTalepService {

    private final MahkemeKararTalepRepo mahkemeKararTalepRepo;

    @Autowired
    public DbMahkemeKararTalepServiceImpl(MahkemeKararTalepRepo repository) {
        super(repository);
        this.mahkemeKararTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeKararTalep> findByEvrakId(Long evrakId) {
        return mahkemeKararTalepRepo.findByEvrakId(evrakId);
    }


}
