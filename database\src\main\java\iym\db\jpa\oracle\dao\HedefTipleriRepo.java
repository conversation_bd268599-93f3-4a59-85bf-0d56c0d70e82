package iym.db.jpa.dao;

import iym.common.model.entity.iym.HedefTipleri;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for HedefTipleri entity
 */
@Repository
public interface HedefTipleriRepo extends JpaRepository<HedefTipleri, Long> {

    Optional<HedefTipleri> findByHedefKodu(Long hedefKodu);
    
    List<HedefTipleri> findByHedefTipi(String hedefTipi);
    
    List<HedefTipleri> findBySonlandirmami(String sonlandirmami);
    
    List<HedefTipleri> findByDurum(String durum);
    
    List<HedefTipleri> findByHedefTanim(String hedefTanim);
    
    List<HedefTipleri> findByAktifmi(Long aktifmi);
    
    List<HedefTipleri> findByHitapaGonderilecekmi(Long hitapaGonderilecekmi);
    
    List<HedefTipleri> findByHedefTipiContainingIgnoreCase(String hedefTipi);
    
    List<HedefTipleri> findAllByOrderBySnoAsc();
}
