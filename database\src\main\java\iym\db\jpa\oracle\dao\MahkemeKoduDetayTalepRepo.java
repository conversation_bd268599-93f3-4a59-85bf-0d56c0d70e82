package iym.db.jpa.dao;

import iym.common.model.entity.iym.MahkemeKoduDetayTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeKoduDetayTalepRepo entity
 */
@Repository
public interface MahkemeKoduDetayTalepRepo extends JpaRepository<MahkemeKoduDetayTalep, Long> {

    Optional<MahkemeKoduDetayTalep> findByMahkemeKararDetayId(Long mahkemeKararTalepId);

}
