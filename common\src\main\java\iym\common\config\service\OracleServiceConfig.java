package iym.common.config.service;

import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * Oracle Service Layer Configuration
 * Scans for Oracle-specific service implementations
 * 
 * Only activated when Oracle EntityManagerFactory is present
 */
@Configuration
@ConditionalOnBean(name = "oracleEntityManagerFactory")
@ComponentScan({
    "iym.db.jpa.service.impl",      // Oracle service implementations
    "iym.common.service.db"         // Common service interfaces
})
public class OracleServiceConfig {
    
    public OracleServiceConfig() {
        // Constructor for logging if needed
    }
}
