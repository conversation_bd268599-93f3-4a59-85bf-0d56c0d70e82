package iym.db.jpa.dao;

import iym.common.model.entity.iym.MahkemeAidiyatDetayTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeAidiyatDetayTalep entity
 */
@Repository
public interface MahkemeAidiyatDetayTalepRepo extends JpaRepository<MahkemeAidiyatDetayTalep, Long> {

    List<MahkemeAidiyatDetayTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);
    
    List<MahkemeAidiyatDetayTalep> findByIliskiliMahkemeKararId(Long iliskiliMahkemeKararId);
    
    List<MahkemeAidiyatDetayTalep> findByMahkemeKararDetayTalepId(Long mahkemeKararDetayTalepId);
    
    List<MahkemeAidiyatDetayTalep> findByDurum(String durum);
    
    List<MahkemeAidiyatDetayTalep> findByTarihBetween(Date startDate, Date endDate);

}
