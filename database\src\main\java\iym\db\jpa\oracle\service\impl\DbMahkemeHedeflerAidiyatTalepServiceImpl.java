package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.MahkemeHedeflerAidiyatTalep;
import iym.common.service.db.DbMahkemeHedeflerAidiyatTalepService;
import iym.db.jpa.dao.MahkemeHedeflerAidiyatTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeHedeflerAidiyatTalep entity
 */
@Service
public class DbMahkemeHedeflerAidiyatTalepServiceImpl extends GenericDbServiceImpl<MahkemeHedeflerAidiyatTalep, Long> implements DbMahkemeHedeflerAidiyatTalepService {

    private final MahkemeHedeflerAidiyatTalepRepo mahkemeHedeflerAidiyatTalepRepo;

    @Autowired
    public DbMahkemeHedeflerAidiyatTalepServiceImpl(MahkemeHedeflerAidiyatTalepRepo repository) {
        super(repository);
        this.mahkemeHedeflerAidiyatTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeHedeflerAidiyatTalep> findByHedefId(Long hedefId) {
        return mahkemeHedeflerAidiyatTalepRepo.findByHedefId(hedefId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeHedeflerAidiyatTalep> findByMahkemeKararId(Long mahkemeKararId) {
        return mahkemeHedeflerAidiyatTalepRepo.findByMahkemeKararId(mahkemeKararId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeHedeflerAidiyatTalep> findByAidiyatKod(String aidiyatKod) {
        return mahkemeHedeflerAidiyatTalepRepo.findByAidiyatKod(aidiyatKod);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeHedeflerAidiyatTalep> findByDurumu(String durumu) {
        return mahkemeHedeflerAidiyatTalepRepo.findByDurumu(durumu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeHedeflerAidiyatTalep> findByKullaniciId(Long kullaniciId) {
        return mahkemeHedeflerAidiyatTalepRepo.findByKullaniciId(kullaniciId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeHedeflerAidiyatTalep> findByTarihBetween(Date startDate, Date endDate) {
        return mahkemeHedeflerAidiyatTalepRepo.findByTarihBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeHedeflerAidiyatTalep> findByHedefIdAndAidiyatKodAndMahkemeKararId(
            Long hedefId, 
            String aidiyatKod, 
            Long mahkemeKararId) {
        return mahkemeHedeflerAidiyatTalepRepo.findByHedefIdAndAidiyatKodAndMahkemeKararId(
                hedefId, aidiyatKod, mahkemeKararId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeHedeflerAidiyatTalep> findByHedefIdAndMahkemeKararId(Long hedefId, Long mahkemeKararId) {
        return mahkemeHedeflerAidiyatTalepRepo.findByHedefIdAndMahkemeKararId(hedefId, mahkemeKararId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeHedeflerAidiyatTalep> findByHedefIdAndAidiyatKod(Long hedefId, String aidiyatKod) {
        return mahkemeHedeflerAidiyatTalepRepo.findByHedefIdAndAidiyatKod(hedefId, aidiyatKod);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeHedeflerAidiyatTalep> findByMahkemeKararIdAndAidiyatKod(Long mahkemeKararId, String aidiyatKod) {
        return mahkemeHedeflerAidiyatTalepRepo.findByMahkemeKararIdAndAidiyatKod(mahkemeKararId, aidiyatKod);
    }
}
