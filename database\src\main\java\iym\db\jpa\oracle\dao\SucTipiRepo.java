package iym.db.jpa.dao;

import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.SucTipi;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for SucTipiRepo entity
 */
@Repository
public interface SucTipiRepo extends JpaRepository<SucTipi, String> {

    Optional<SucTipi> findBySucTipiKodu(String sucTipiKodu);

}
