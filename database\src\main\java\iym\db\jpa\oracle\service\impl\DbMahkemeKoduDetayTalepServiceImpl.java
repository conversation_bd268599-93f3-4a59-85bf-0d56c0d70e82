package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.MahkemeKarar;
import iym.common.model.entity.iym.MahkemeKoduDetayTalep;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.service.db.DbMahkemeKoduDetayTalepService;
import iym.db.jpa.dao.MahkemeKararRepo;
import iym.db.jpa.dao.MahkemeKoduDetayTalepRepo;
import iym.db.jpa.dao.MakosKararRequestLogRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeKararTalep entity
 */
@Service
public class DbMahkemeKoduDetayTalepServiceImpl extends GenericDbServiceImpl<MahkemeKoduDetayTalep, Long> implements DbMahkemeKoduDetayTalepService {

    private final MahkemeKoduDetayTalepRepo mahkemeKoduDetayTalepRepo;

    @Autowired
    public DbMahkemeKoduDetayTalepServiceImpl(MahkemeKoduDetayTalepRepo repository) {
        super(repository);
        this.mahkemeKoduDetayTalepRepo = repository;
    }

    @Override
    public Optional<MahkemeKoduDetayTalep> findByMahkemeKararDetayId(Long mahkemeKararDetayId){
        return mahkemeKoduDetayTalepRepo.findByMahkemeKararDetayId(mahkemeKararDetayId);
    }
}
