package iym.db.jpa.dao;

import iym.common.model.entity.iym.HedeflerDetayTalep;
import iym.common.model.entity.iym.HedeflerTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Repository interface for HedeflerTalep entity
 */
@Repository
public interface HedeflerDetayTalepRepo extends JpaRepository<HedeflerDetayTalep, Long> {

    List<HedeflerDetayTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);


}
