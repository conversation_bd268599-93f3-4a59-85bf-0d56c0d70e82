package iym.db.jpa.dao;

import iym.common.model.entity.iym.HtsHedeflerTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Repository interface for HtsHedeflerTalep entity
 */
@Repository
public interface HtsHedeflerTalepRepo extends JpaRepository<HtsHedeflerTalep, Long> {

    List<HtsHedeflerTalep> findByMahkemeKararId(Long mahkemeKararId);
    
  }
