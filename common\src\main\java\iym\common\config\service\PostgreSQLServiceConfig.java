package iym.common.config.service;

import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * PostgreSQL Service Layer Configuration
 * Scans for PostgreSQL-specific service implementations
 * 
 * Only activated when PostgreSQL EntityManagerFactory is present
 */
@Configuration
@ConditionalOnBean(name = "postgresqlEntityManagerFactory")
@ComponentScan({
    "iym.db.jpa.postgresql.service.impl",  // PostgreSQL service implementations
    "iym.common.service.db"                // Common service interfaces (shared)
})
public class PostgreSQLServiceConfig {
    
    public PostgreSQLServiceConfig() {
        // Constructor for logging if needed
    }
}
