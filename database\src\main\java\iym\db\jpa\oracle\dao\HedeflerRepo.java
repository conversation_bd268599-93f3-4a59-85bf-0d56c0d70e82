package iym.db.jpa.dao;

import iym.common.model.api.HedefTip;
import iym.common.model.entity.iym.Hedefler;
import iym.common.model.entity.iym.HedeflerTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for HedeflerTalep entity
 */
@Repository
public interface HedeflerRepo extends JpaRepository<Hedefler, Long> {

   // @Override
   // Optional<Hedefler> findById(Long id);

    Optional<Hedefler> findByMahkemeKararIdAndHedefNoAndHedefTipi(Long mahkemeKararId, String hedefNo, String hedefTipi);

    List<Hedefler> findByMahkemeKararId(Long mahkemeKararId);


}
