package iym.db.jpa.dao;

import iym.common.model.entity.iym.HtsMahkemeKararTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for HtsMahkemeKararTalep entity
 */
@Repository
public interface HtsMahkemeKararTalepRepo extends JpaRepository<HtsMahkemeKararTalep, Long> {

    List<HtsMahkemeKararTalep> findByEvrakId(Long evrakId);
    
    List<HtsMahkemeKararTalep> findByKullaniciId(Long kullaniciId);
    
    List<HtsMahkemeKararTalep> findByDurum(String durum);
    
    List<HtsMahkemeKararTalep> findByKararTip(String kararTip);
    
    List<HtsMahkemeKararTalep> findByHukukBirim(String hukukBirim);
    
    List<HtsMahkemeKararTalep> findByMahkemeIli(String mahkemeIli);
    
    List<HtsMahkemeKararTalep> findByMahkemeKodu(String mahkemeKodu);
    
    List<HtsMahkemeKararTalep> findByMahkemeAdiContainingIgnoreCase(String mahkemeAdi);
    
    Optional<HtsMahkemeKararTalep> findByMahkemeKararNo(String mahkemeKararNo);
    
    List<HtsMahkemeKararTalep> findBySorusturmaNo(String sorusturmaNo);
    
    List<HtsMahkemeKararTalep> findByKayitTarihiBetween(Date startDate, Date endDate);
    
    List<HtsMahkemeKararTalep> findByMahkemeIliAndMahkemeKodu(String mahkemeIli, String mahkemeKodu);
    
    List<HtsMahkemeKararTalep> findByKararTipAndDurum(String kararTip, String durum);
    
    boolean existsByMahkemeKararNo(String mahkemeKararNo);
}
