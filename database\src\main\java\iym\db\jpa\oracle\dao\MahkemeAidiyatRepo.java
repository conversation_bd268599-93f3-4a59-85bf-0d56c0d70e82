package iym.db.jpa.dao;

import iym.common.model.entity.iym.MahkemeAidiyat;
import iym.common.model.entity.iym.MahkemeAidiyatTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeAidiyat entity
 */
@Repository
public interface MahkemeAidiyatRepo extends JpaRepository<MahkemeAidiyat, Long> {

    List<MahkemeAidiyat> findByMahkemeKararId(Long mahkemeKararId);

    Optional<MahkemeAidiyat> findByMahkemeKararIdAndAidiyatKod(Long mahkemeKararId, String aidiyatKod);
}
