package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.MahkemeKararTalep;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.DbMahkemeKararTalepService;
import iym.db.jpa.dao.MahkemeBilgisiRepo;
import iym.db.jpa.dao.MahkemeKararTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeBilgi entity
 */
@Service
public class DbMahkemeBilgiServiceImpl extends GenericDbServiceImpl<MahkemeBilgi, Long> implements DbMahkemeBilgiService {

    private final MahkemeBilgisiRepo mahkemeBilgisiRepo;

    @Autowired
    public DbMahkemeBilgiServiceImpl(MahkemeBilgisiRepo repository) {
        super(repository);
        this.mahkemeBilgisiRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeBilgi> findByMahkemeKodu(String mahkemeKodu){
        return mahkemeBilgisiRepo.findByMahkemeKodu(mahkemeKodu);
    }


}
