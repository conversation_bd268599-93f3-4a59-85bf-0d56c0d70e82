package iym.spring.db.loader;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Legacy DbLoader - Will be replaced by OracleJpaConfig
 * This class is kept for reference but should not be used in multi-datasource setup
 */
@Configuration
@EnableTransactionManagement
@ConditionalOnMissingBean(name = "oracleEntityManagerFactory")
@ComponentScan({"iym.db"})
@EntityScan(basePackages={"iym.common.model.entity"})
@EnableJpaRepositories(basePackages={"iym.db"})
@Slf4j
public class DbLoader {

    public DbLoader(){
        log.info("DbLoader initializing - Legacy Single DataSource Mode");
        log.warn("Consider migrating to OracleJpaConfig for better multi-datasource support");
    }
}
