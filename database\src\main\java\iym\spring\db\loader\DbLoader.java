package iym.spring.db.loader;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Legacy DbLoader - DEPRECATED
 * This class has been replaced by OracleJpaConfig for multi-datasource support
 *
 * @deprecated Use OracleJpaConfig instead
 */
@Deprecated
// @Configuration - Commented out to disable this configuration
// @EnableTransactionManagement
// @ConditionalOnMissingBean(name = "oracleEntityManagerFactory")
// @ComponentScan({"iym.db"})
// @EntityScan(basePackages={"iym.common.model.entity"})
// @EnableJpaRepositories(basePackages={"iym.db"})
@Slf4j
public class DbLoader {

    public DbLoader(){
        log.warn("DbLoader is deprecated. Use OracleJpaConfig instead.");
    }
}
