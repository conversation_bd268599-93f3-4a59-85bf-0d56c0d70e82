package iym.db.jpa.service.impl;

import iym.common.model.api.KullaniciKurum;
import iym.common.model.entity.iym.IymUser;
import iym.common.model.enums.IymUserRoleType;
import iym.common.model.enums.UserStatusType;
import iym.common.util.ExceptionUtils;
import iym.db.jpa.oracle.dao.IymUserRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DbIymUserServiceImplTest {

    @Mock
    private IymUserRepo iymUserRepo;

    @Mock
    private PasswordEncoder passwordEncoder;

    private iym.db.jpa.service.impl.DbIymUserServiceImpl dbIymUserService;

    private IymUser iymUser;
    private IymUser iymUser2;
    private List<IymUser> iymUserList;
    private UUID testUserId;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();

        // Constructor injection - clean and testable approach
        dbIymUserService = new iym.db.jpa.service.impl.DbIymUserServiceImpl(iymUserRepo, passwordEncoder);

        iymUser = IymUser.builder()
                .id(testUserId)
                .username("test_user")
                .password("encoded_password")
                .status(UserStatusType.ACTIVE)
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();

        iymUser2 = IymUser.builder()
                .id(UUID.randomUUID())
                .username("test_user2")
                .password("encoded_password2")
                .status(UserStatusType.PASSIVE)
                .role(IymUserRoleType.ROLE_ADLI)
                .kurum(KullaniciKurum.ADLI)
                .build();

        iymUserList = Arrays.asList(iymUser, iymUser2);
    }

    @Test
    void findByUsername_shouldReturnUser_whenExists() {
        // Given
        when(iymUserRepo.findByUsername("test_user")).thenReturn(Optional.of(iymUser));

        // When
        Optional<IymUser> result = dbIymUserService.findByUsername("test_user");

        // Then
        assertThat(result).isPresent();
        assertThat(result.get()).isEqualTo(iymUser);
        verify(iymUserRepo).findByUsername("test_user");
    }

    @Test
    void findByUsername_shouldReturnEmpty_whenNotExists() {
        // Given
        when(iymUserRepo.findByUsername("nonexistent")).thenReturn(Optional.empty());

        // When
        Optional<IymUser> result = dbIymUserService.findByUsername("nonexistent");

        // Then
        assertThat(result).isEmpty();
        verify(iymUserRepo).findByUsername("nonexistent");
    }

    @Test
    void findByStatus_shouldReturnUserList() {
        // Given
        List<IymUser> activeUsers = Arrays.asList(iymUser);
        when(iymUserRepo.findByStatus(UserStatusType.ACTIVE)).thenReturn(activeUsers);

        // When
        List<IymUser> result = dbIymUserService.findByStatus(UserStatusType.ACTIVE);

        // Then
        assertThat(result).isEqualTo(activeUsers);
        verify(iymUserRepo).findByStatus(UserStatusType.ACTIVE);
    }

    @Test
    void findByRoleOrderByUsernameAsc_shouldReturnSortedUserList() {
        // Given
        List<IymUser> adminUsers = Arrays.asList(iymUser);
        when(iymUserRepo.findByRoleOrderByUsernameAsc(IymUserRoleType.ROLE_ADMIN)).thenReturn(adminUsers);

        // When
        List<IymUser> result = dbIymUserService.findByRoleOrderByUsernameAsc(IymUserRoleType.ROLE_ADMIN);

        // Then
        assertThat(result).isEqualTo(adminUsers);
        verify(iymUserRepo).findByRoleOrderByUsernameAsc(IymUserRoleType.ROLE_ADMIN);
    }

    @Test
    void findAllByOrderByUsernameAsc_shouldReturnSortedUserList() {
        // Given
        when(iymUserRepo.findAllByOrderByUsernameAsc()).thenReturn(iymUserList);

        // When
        List<IymUser> result = dbIymUserService.findAllByOrderByUsernameAsc();

        // Then
        assertThat(result).isEqualTo(iymUserList);
        verify(iymUserRepo).findAllByOrderByUsernameAsc();
    }

    @Test
    void activateUser_shouldSetStatusToActive() {
        // Given
        when(iymUserRepo.findByUsername("test_user")).thenReturn(Optional.of(iymUser));

        // When
        dbIymUserService.activateUser("test_user");

        // Then
        assertThat(iymUser.getStatus()).isEqualTo(UserStatusType.ACTIVE);
        verify(iymUserRepo).findByUsername("test_user");
        verify(iymUserRepo).save(iymUser);
    }

    @Test
    void activateUser_shouldThrowException_whenUserNotFound() {
        // Given
        when(iymUserRepo.findByUsername("nonexistent")).thenReturn(Optional.empty());

        // When/Then
        assertThrows(RuntimeException.class, () -> dbIymUserService.activateUser("nonexistent"));
        verify(iymUserRepo).findByUsername("nonexistent");
        verify(iymUserRepo, never()).save(any());
    }

    @Test
    void deactivateUser_shouldSetStatusToPassive() {
        // Given
        when(iymUserRepo.findByUsername("test_user")).thenReturn(Optional.of(iymUser));

        // When
        dbIymUserService.deactivateUser("test_user");

        // Then
        assertThat(iymUser.getStatus()).isEqualTo(UserStatusType.PASSIVE);
        verify(iymUserRepo).findByUsername("test_user");
        verify(iymUserRepo).save(iymUser);
    }

    @Test
    void updateUser_shouldUpdateRoleAndKurum() {
        // Given
        IymUser existingUser = IymUser.builder()
                .id(testUserId)
                .username("test_user")
                .password("old_password")
                .status(UserStatusType.ACTIVE)
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();

        IymUser updateUser = IymUser.builder()
                .username("test_user")
                .role(IymUserRoleType.ROLE_ADLI)
                .kurum(KullaniciKurum.ADLI)
                .build();

        when(iymUserRepo.findByUsername("test_user")).thenReturn(Optional.of(existingUser));

        // When
        dbIymUserService.updateUser(updateUser);

        // Then
        assertThat(existingUser.getRole()).isEqualTo(IymUserRoleType.ROLE_ADLI);
        assertThat(existingUser.getKurum()).isEqualTo(KullaniciKurum.ADLI);
        verify(iymUserRepo).findByUsername("test_user");
        verify(iymUserRepo).save(existingUser);
    }

    @Test
    void updateUser_shouldUpdatePassword_whenNewPasswordProvided() {
        // Given
        IymUser existingUser = IymUser.builder()
                .id(testUserId)
                .username("test_user")
                .password("old_encoded_password")
                .status(UserStatusType.ACTIVE)
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();

        IymUser updateUser = IymUser.builder()
                .username("test_user")
                .newPassword("new_password")
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();

        when(iymUserRepo.findByUsername("test_user")).thenReturn(Optional.of(existingUser));
        when(passwordEncoder.matches("new_password", "old_encoded_password")).thenReturn(false);
        when(passwordEncoder.encode("new_password")).thenReturn("new_encoded_password");

        // When
        dbIymUserService.updateUser(updateUser);

        // Then
        assertThat(existingUser.getPassword()).isEqualTo("new_encoded_password");
        verify(iymUserRepo).findByUsername("test_user");
        verify(passwordEncoder).matches("new_password", "old_encoded_password");
        verify(passwordEncoder).encode("new_password");
        verify(iymUserRepo).save(existingUser);
    }

    @Test
    void updateUser_shouldThrowException_whenNewPasswordSameAsOld() {
        // Given
        IymUser existingUser = IymUser.builder()
                .id(testUserId)
                .username("test_user")
                .password("old_encoded_password")
                .status(UserStatusType.ACTIVE)
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();

        IymUser updateUser = IymUser.builder()
                .username("test_user")
                .newPassword("same_password")
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();

        when(iymUserRepo.findByUsername("test_user")).thenReturn(Optional.of(existingUser));
        when(passwordEncoder.matches("same_password", "old_encoded_password")).thenReturn(true);

        // When/Then
        assertThrows(RuntimeException.class, () -> dbIymUserService.updateUser(updateUser));
        verify(iymUserRepo).findByUsername("test_user");
        verify(passwordEncoder).matches("same_password", "old_encoded_password");
        verify(passwordEncoder, never()).encode(any());
        verify(iymUserRepo, never()).save(any());
    }
}
