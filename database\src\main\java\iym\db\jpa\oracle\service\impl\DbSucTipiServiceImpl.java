package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.SucTipi;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.DbSucTipiService;
import iym.db.jpa.dao.MahkemeBilgisiRepo;
import iym.db.jpa.dao.SucTipiRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Service implementation for SucTipi entity
 */
@Service
public class DbSucTipiServiceImpl extends GenericDbServiceImpl<SucTipi, String> implements DbSucTipiService {

    private final SucTipiRepo sucTipiRepo;

    @Autowired
    public DbSucTipiServiceImpl(SucTipiRepo repository) {
        super(repository);
        this.sucTipiRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<SucTipi> findBySucTipiKodu(String sucTipiKodu){
        return sucTipiRepo.findBySucTipiKodu(sucTipiKodu);
    }


}
